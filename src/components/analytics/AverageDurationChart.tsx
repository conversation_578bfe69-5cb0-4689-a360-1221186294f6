'use client';

import { Area<PERSON>hart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton } from '@/components/blocks/analytics-skeletons';
import type { ApiData } from '@/lib/types';

interface HourlyData {
  name: string;
  calls: number;
}

interface AverageDurationChartProps {
  hourlyData: HourlyData[];
  analyticsData: ApiData | null;
  loading: boolean;
}

export function AverageDurationChart({ hourlyData, analyticsData, loading }: AverageDurationChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Average Duration by Hour</CardTitle>
          <CardDescription>Call duration patterns throughout the day</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  const processedData = hourlyData.map((data, index) => ({
    ...data,
    avgDuration: analyticsData?.calls
      ? analyticsData.calls
          .filter((call) => new Date(call.start_timestamp).getHours() === index + 8)
          .reduce((acc, call) => acc + call.duration_ms / 60000, 0) /
        Math.max(
          1,
          analyticsData.calls.filter(
            (call) => new Date(call.start_timestamp).getHours() === index + 8,
          ).length,
        )
      : 0,
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Average Duration by Hour</CardTitle>
        <CardDescription>Call duration patterns throughout the day</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={processedData}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                borderRadius: 'var(--radius)',
                backgroundColor: 'var(--card)',
                color: 'var(--card-foreground)',
                border: '1px solid var(--muted)',
              }}
              formatter={(value) => [
                `${typeof value === 'number' ? value.toFixed(1) : value} min`,
                'Avg Duration',
              ]}
            />
            <Area
              type="monotone"
              dataKey="avgDuration"
              stroke="#f59e0b"
              fill="#f59e0b"
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
