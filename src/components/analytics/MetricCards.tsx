'use client';

import { Clock, Phone, PhoneCall, PhoneMissed } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MetricCardSkeleton } from '@/components/blocks/analytics-skeletons';
import type { ApiData } from '@/lib/types';

interface MetricCardsProps {
  analyticsData: ApiData | null;
  analyticsLoading: boolean;
  timePeriod: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export function MetricCards({ analyticsData, analyticsLoading, timePeriod }: MetricCardsProps) {
  if (analyticsLoading) {
    return (
      <>
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </>
    );
  }

  const totalCalls = analyticsData?.calls?.length || 0;
  const answeredCalls = analyticsData?.calls?.filter(
    (call) => call.call_analysis?.call_successful,
  ).length || 0;
  const missedCalls = analyticsData?.calls?.filter(
    (call) => !call.call_analysis?.call_successful,
  ).length || 0;

  const getTimePeriodText = () => {
    switch (timePeriod) {
      case 'day':
        return 'Last 24 hours';
      case 'week':
        return 'Last 7 days';
      case 'month':
        return 'Last 30 days';
      case 'quarter':
        return 'Last 90 days';
      case 'year':
        return 'Last year';
      default:
        return 'Last 30 days';
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
          <Phone className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">{totalCalls}</div>
          <p className="text-sm text-cyan-600 dark:text-cyan-400 mt-2">
            {getTimePeriodText()}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Answered Calls</CardTitle>
          <PhoneCall className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">{answeredCalls}</div>
          <p className="text-sm text-green-600 dark:text-green-400 mt-2">
            {totalCalls > 0
              ? `${Math.round((answeredCalls / totalCalls) * 100)}% success rate`
              : 'No data available'}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Missed Calls</CardTitle>
          <PhoneMissed className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">{missedCalls}</div>
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            {totalCalls > 0
              ? `${Math.round((missedCalls / totalCalls) * 100)}% missed rate`
              : 'No data available'}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Avg. Call Duration</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">
            {analyticsData?.summary
              ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
              : '0m'}
          </div>
          <p className="text-sm mt-2 text-muted-foreground">Average duration</p>
        </CardContent>
      </Card>
    </>
  );
}
