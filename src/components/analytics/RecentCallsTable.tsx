'use client';

import { Check, ExternalLink, PhoneMissed, Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { EmptyState } from '@/components/blocks/analytics-skeletons';

interface RecentCall {
  id: number;
  call_id: string;
  name: string;
  time: string;
  duration: string;
  status: string;
  recording_url?: string;
  public_log_url?: string;
  sentiment: string;
}

interface RecentCallsTableProps {
  data: RecentCall[];
  loading: boolean;
}

export function RecentCallsTable({ data, loading }: RecentCallsTableProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Calls</CardTitle>
          <CardDescription>Your most recent incoming calls</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Calls</CardTitle>
          <CardDescription>Your most recent incoming calls</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No recent calls"
            description="No call data available to display."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Calls</CardTitle>
        <CardDescription>Your most recent incoming calls</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Call ID</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Sentiment</TableHead>
              <TableHead>Recording</TableHead>
              <TableHead>Log</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((call) => (
              <TableRow key={call.id}>
                <TableCell className="font-medium">{call.name}</TableCell>
                <TableCell>{call.time}</TableCell>
                <TableCell>{call.duration}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {call.status === 'answered' ? (
                      <Badge variant="success">
                        <Check className="h-3 w-3 mr-1" /> Successful
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <PhoneMissed className="h-3 w-3 mr-1" /> Missed
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      call.sentiment.toLowerCase() === 'positive'
                        ? 'success'
                        : call.sentiment.toLowerCase() === 'negative'
                          ? 'destructive'
                          : call.sentiment.toLowerCase() === 'neutral'
                            ? 'secondary'
                            : 'outline'
                    }
                  >
                    {call.sentiment}
                  </Badge>
                </TableCell>
                <TableCell>
                  {call.recording_url ? (
                    <Button variant="outline" size="sm" asChild>
                      <a
                        href={call.recording_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1"
                      >
                        <Play className="h-3 w-3" />
                        Play
                      </a>
                    </Button>
                  ) : (
                    <span className="text-muted-foreground">N/A</span>
                  )}
                </TableCell>
                <TableCell>
                  {call.public_log_url ? (
                    <Button variant="outline" size="sm" asChild>
                      <a
                        href={call.public_log_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        View
                      </a>
                    </Button>
                  ) : (
                    <span className="text-muted-foreground">N/A</span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
