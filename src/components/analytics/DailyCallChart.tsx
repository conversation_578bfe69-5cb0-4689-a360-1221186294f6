'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton, EmptyState } from '@/components/blocks/analytics-skeletons';
import type { ProcessedCallData } from '@/lib/types';

interface DailyCallChartProps {
  data: ProcessedCallData[];
  loading: boolean;
}

export function DailyCallChart({ data, loading }: DailyCallChartProps) {
  if (loading) {
    return (
      <Card className="lg:col-span-4">
        <CardHeader>
          <CardTitle>Daily Call Volume</CardTitle>
          <CardDescription>Number of calls per day this week</CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <ChartSkeleton height="h-[400px]" />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card className="lg:col-span-4">
        <CardHeader>
          <CardTitle>Daily Call Volume</CardTitle>
          <CardDescription>Number of calls per day this week</CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <EmptyState
            title="No daily data"
            description="No call data available for the selected period."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="lg:col-span-4">
      <CardHeader>
        <CardTitle>Daily Call Volume</CardTitle>
        <CardDescription>Number of calls per day this week</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar
              dataKey="successful"
              name="Successful Calls"
              fill="#10b981"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="unsuccessful"
              name="Unsuccessful Calls"
              fill="#ef4444"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
