'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton, EmptyState } from '@/components/blocks/analytics-skeletons';
import type { ProcessedCallData } from '@/lib/types';

interface MonthlyCallChartProps {
  data: ProcessedCallData[];
  loading: boolean;
}

export function MonthlyCallChart({ data, loading }: MonthlyCallChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Call Volume</CardTitle>
          <CardDescription>Call volume over the past 30 days</CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <ChartSkeleton height="h-[400px]" />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Call Volume</CardTitle>
          <CardDescription>Call volume over the past 30 days</CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <EmptyState
            title="No monthly data"
            description="No call data available for the past 30 days."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Call Volume</CardTitle>
        <CardDescription>Call volume over the past 30 days</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                borderRadius: 'var(--radius)',
                backgroundColor: 'var(--card)',
                color: 'var(--card-foreground)',
                border: '1px solid var(--muted)',
              }}
            />
            <Legend />
            <Bar
              dataKey="successful"
              name="Successful Calls"
              fill="#10b981"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="unsuccessful"
              name="Unsuccessful Calls"
              fill="#ef4444"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
