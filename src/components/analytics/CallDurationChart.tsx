'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton, EmptyState } from '@/components/blocks/analytics-skeletons';

interface DurationData {
  name: string;
  value: number;
}

interface CallDurationChartProps {
  data: DurationData[];
  loading: boolean;
}

export function CallDurationChart({ data, loading }: CallDurationChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Duration</CardTitle>
          <CardDescription>Distribution of call lengths</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.value === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Duration</CardTitle>
          <CardDescription>Distribution of call lengths</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No duration data"
            description="No call duration data available."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Call Duration</CardTitle>
        <CardDescription>Distribution of call lengths</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              stroke="var(--muted)"
              dataKey="value"
            >
              {data.map((_, index) => (
                <Cell
                  key={`cell-${index}`}
                  className={
                    [
                      'fill-emerald-400',
                      'fill-emerald-500',
                      'fill-amber-400',
                      'fill-orange-500',
                      'fill-rose-500',
                    ][index % 5]
                  }
                />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
