'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton, EmptyState } from '@/components/blocks/analytics-skeletons';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { SentimentData } from '@/lib/types';

const sentimentChartConfig = {
  positive: {
    label: 'Positive',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  neutral: {
    label: 'Neutral',
    color: 'hsl(45, 93%, 47%)', // Yellow
  },
  negative: {
    label: 'Negative',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

interface CustomerSentimentChartProps {
  data: SentimentData[];
  loading: boolean;
}

export function CustomerSentimentChart({ data, loading }: CustomerSentimentChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Customer Sentiment (30 Days)</CardTitle>
          <CardDescription>Sentiment analysis of customer calls</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Customer Sentiment (30 Days)</CardTitle>
          <CardDescription>Sentiment analysis of customer calls</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No sentiment data"
            description="No customer sentiment data available for analysis."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer Sentiment (30 Days)</CardTitle>
        <CardDescription>Sentiment analysis of customer calls</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={sentimentChartConfig} className="mx-auto max-h-72">
          <PieChart>
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={70}
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry) => (
                <Cell key={entry.name} fill={entry.fill} />
              ))}
            </Pie>
            <Legend />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
