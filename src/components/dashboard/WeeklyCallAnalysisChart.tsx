'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Legend } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartSkeleton, EmptyState } from '@/components/blocks/analytics-skeletons';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { ProcessedCallData } from '@/lib/types';

const callVolumeChartConfig = {
  calls: {
    label: 'Total Calls',
    color: 'hsl(217, 91%, 60%)', // Blue
  },
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

interface WeeklyCallAnalysisChartProps {
  data: ProcessedCallData[];
  loading: boolean;
}

export function WeeklyCallAnalysisC<PERSON>({ data, loading }: WeeklyCallAnalysisChartProps) {
  if (loading) {
    return (
      <Card className="lg:col-span-4">
        <CardHeader>
          <CardTitle>Weekly Call Analysis</CardTitle>
          <CardDescription>
            Successful and unsuccessful calls over the last 7 days.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton height="h-[310px]" />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card className="lg:col-span-4">
        <CardHeader>
          <CardTitle>Weekly Call Analysis</CardTitle>
          <CardDescription>
            Successful and unsuccessful calls over the last 7 days.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No weekly data"
            description="No call data available for the last 7 days."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="lg:col-span-4">
      <CardHeader>
        <CardTitle>Weekly Call Analysis</CardTitle>
        <CardDescription>
          Successful and unsuccessful calls over the last 7 days.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={callVolumeChartConfig} className="h-[310px] w-full">
          <BarChart data={data} accessibilityLayer>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <YAxis allowDecimals={false} tickMargin={8} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Legend />
            <Bar
              dataKey="successful"
              stackId="a"
              fill="var(--color-successful)"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="unsuccessful"
              stackId="a"
              fill="var(--color-unsuccessful)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
