'use client';

import { useState, useEffect, useMemo } from 'react';
import { Building2, Users } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  AreaChart,
  Area,
  Legend,
} from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { IUser, fetchAdminAnalyticsData, getAllUsers } from '@/actions/admin';
import { ApiData, Call } from '@/lib/types';

// Chart configurations with improved colors and styling
// const callTypeChartConfig = {
//   successful: {
//     label: 'Successful',
//     color: 'hsl(142, 76%, 36%)', // Green
//   },
//   unsuccessful: {
//     label: 'Unsuccessful',
//     color: 'hsl(0, 84%, 60%)', // Red
//   },
// } satisfies ChartConfig;

const callVolumeChartConfig = {
  calls: {
    label: 'Calls',
    color: 'hsl(217, 91%, 60%)', // Blue
  },
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

// const sentimentChartConfig = {
//   positive: {
//     label: 'Positive',
//     color: 'hsl(142, 76%, 36%)', // Green
//   },
//   neutral: {
//     label: 'Neutral',
//     color: 'hsl(45, 93%, 47%)', // Yellow
//   },
//   negative: {
//     label: 'Negative',
//     color: 'hsl(0, 84%, 60%)', // Red
//   },
// } satisfies ChartConfig;

export default function DashboardPage() {
  const { selectedClinic, clinics, loading } = useAdminClinic();
  const { getTokens } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<ApiData | null>(null);
  // const [ analyticsLoading, setAnalyticsLoading] = useState(false);
  const [allUsersLoading, setAllUsersLoading] = useState(false);
  const [users, setUsers]=useState<IUser[] |null>(null);

  // Enhanced data processing functions with better analytics
  const processCallsData = useMemo(() => {
    return (calls: Call[], period: 'day' | 'week' | 'month') => {
      if (!calls || calls.length === 0) return [];

      const now = new Date();
      const dataMap = new Map<
        string,
        {
          calls: number;
          successful: number;
          unsuccessful: number;
          avgDuration: number;
          totalDuration: number;
          count: number;
        }
      >();

      if (period === 'day') {
        // Last 24 hours, grouped by hour
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i, 0, 0, 0);
          const key = hour.getHours().toString().padStart(2, '0') + ':00';
          dataMap.set(key, {
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            avgDuration: 0,
            totalDuration: 0,
            count: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          const hourKey =
            callDate.getHours().toString().padStart(2, '0') + ':00';
          const data = dataMap.get(hourKey);
          if (data) {
            data.calls += 1;
            data.successful += call.call_analysis?.call_successful ? 1 : 0;
            data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            data.totalDuration += call.duration_ms || 0;
            data.count += 1;
            data.avgDuration = data.totalDuration / data.count / 1000 / 60; // Convert to minutes
          }
        });

        return Array.from(dataMap.entries()).map(([time, data]) => ({
          name: time,
          calls: data.calls,
          successful: data.successful,
          unsuccessful: data.unsuccessful,
          avgDuration: Math.round(data.avgDuration * 10) / 10,
        }));
      } else if (period === 'week') {
        // Last 7 days
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          const dayName = days[date.getDay()];
          dataMap.set(dayName, {
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            avgDuration: 0,
            totalDuration: 0,
            count: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          const dayName = days[callDate.getDay()];
          const data = dataMap.get(dayName);
          if (data) {
            data.calls += 1;
            data.successful += call.call_analysis?.call_successful ? 1 : 0;
            data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            data.totalDuration += call.duration_ms || 0;
            data.count += 1;
            data.avgDuration = data.totalDuration / data.count / 1000 / 60;
          }
        });

        return Array.from(dataMap.entries()).map(([name, data]) => ({
          name,
          calls: data.calls,
          successful: data.successful,
          unsuccessful: data.unsuccessful,
          avgDuration: Math.round(data.avgDuration * 10) / 10,
        }));
      } else {
        // Last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          const key = date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
          });
          dataMap.set(key, {
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            avgDuration: 0,
            totalDuration: 0,
            count: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          const dateKey = callDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
          });
          const data = dataMap.get(dateKey);
          if (data) {
            data.calls += 1;
            data.successful += call.call_analysis?.call_successful ? 1 : 0;
            data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            data.totalDuration += call.duration_ms || 0;
            data.count += 1;
            data.avgDuration = data.totalDuration / data.count / 1000 / 60;
          }
        });

        return Array.from(dataMap.entries()).map(([date, data]) => ({
          name: date,
          calls: data.calls,
          successful: data.successful,
          unsuccessful: data.unsuccessful,
          avgDuration: Math.round(data.avgDuration * 10) / 10,
        }));
      }
    };
  }, []);

  // Process call type data (successful vs unsuccessful)
  const processCallTypeData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) {
        return [
          { name: 'Successful', value: 0, fill: 'var(--color-successful)' },
          { name: 'Unsuccessful', value: 0, fill: 'var(--color-unsuccessful)' },
        ];
      }

      const successful = calls.filter(
        (call) => call.call_analysis?.call_successful,
      ).length;
      const unsuccessful = calls.length - successful;

      return [
        {
          name: 'Successful',
          value: successful,
          fill: 'var(--color-successful)',
        },
        {
          name: 'Unsuccessful',
          value: unsuccessful,
          fill: 'var(--color-unsuccessful)',
        },
      ];
    };
  }, []);

  // Process sentiment data
  const processSentimentData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const sentimentCounts = calls.reduce(
        (acc, call) => {
          const sentiment =
            call?.call_analysis?.user_sentiment?.toLowerCase() || 'unknown';
          if (sentiment.includes('positive')) {
            acc.positive += 1;
          } else if (sentiment.includes('negative')) {
            acc.negative += 1;
          } else {
            acc.neutral += 1;
          }
          return acc;
        },
        { positive: 0, neutral: 0, negative: 0 },
      );

      return [
        {
          name: 'Positive',
          value: sentimentCounts.positive,
          fill: 'var(--color-positive)',
        },
        {
          name: 'Neutral',
          value: sentimentCounts.neutral,
          fill: 'var(--color-neutral)',
        },
        {
          name: 'Negative',
          value: sentimentCounts.negative,
          fill: 'var(--color-negative)',
        },
      ].filter((item) => item.value > 0);
    };
  }, []);

// Fetch Analytics Data
useEffect(() => {
  const fetchAnalyticsData = async () => {
    if (!selectedClinic?._id) return;

    // setAnalyticsLoading(true);
    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        console.error('Missing token(s)');
        return;
      }

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30); // Last 30 days

      const response = await fetchAdminAnalyticsData(
        accessToken,
        idToken,
        selectedClinic._id,
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0],
      );

      if (response.ok && response.data) {
        setAnalyticsData(response.data);
      } else {
        console.error('Failed to fetch analytics data:', response.error);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      // setAnalyticsLoading(false);
    }
  };

  fetchAnalyticsData();
}, [selectedClinic?._id, getTokens]);

// Fetch All Users
useEffect(() => {
  const fetchUsersData = async () => {
    if (!selectedClinic?._id) return;

    setAllUsersLoading(true);
    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        console.error('Missing token(s)');
        return;
      }

      const result = await getAllUsers(accessToken, idToken);
      if (result.ok && result.data) {
        setUsers(result.data.data);
      } else {
        console.error('Failed to fetch users:', result.error);
      }
    } catch (error) {
      console.error('Error fetching users data:', error);
    } finally {
      setAllUsersLoading(false);
    }
  };

  fetchUsersData();
}, [selectedClinic?._id, getTokens]);


  // Generate chart data from API with memoization for performance
  const chartData = useMemo(() => {
    if (!analyticsData?.calls) {
      return {
        todayCallData: [],
        weeklyCallData: [],
        monthlyCallData: [],
        callTypeData: [],
        sentimentData: [],
      };
    }

    return {
      todayCallData: processCallsData(analyticsData.calls, 'day'),
      weeklyCallData: processCallsData(analyticsData.calls, 'week'),
      monthlyCallData: processCallsData(analyticsData.calls, 'month'),
      callTypeData: processCallTypeData(analyticsData.calls),
      sentimentData: processSentimentData(analyticsData.calls),
    };
  }, [
    analyticsData,
    processCallsData,
    processCallTypeData,
    processSentimentData,
  ]);

  const {
    todayCallData,
    weeklyCallData,
    monthlyCallData,
  } = chartData;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          {selectedClinic && (
            <p className="text-sm text-muted-foreground mt-1">
              {selectedClinic.clinic_name} • {clinics.length} clinic
              {clinics.length !== 1 ? 's' : ''} total
            </p>
          )}
          {loading && (
            <p className="text-sm text-muted-foreground mt-1">
              Loading clinics...
            </p>
          )}
        </div>
        <div className="mt-2 sm:mt-0">
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allUsersLoading
                ? '...'
                : users?.length || 0}
                </div>
            {/* <p className="text-xs text-muted-foreground">+2 new this month</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clinics</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clinics.length}</div>
            {/* <p className="text-xs text-muted-foreground">+1 new this month</p> */}
          </CardContent>
        </Card>
      </div>

      {/* Analytics Overview Cards */}

      {/* Time-based Analytics with Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Call Analytics</CardTitle>
          <CardDescription>
            Detailed call volume and success rate analysis across different time
            periods
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="today" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="today">Today (24h)</TabsTrigger>
              <TabsTrigger value="week">This Week</TabsTrigger>
              <TabsTrigger value="month">Last 30 Days</TabsTrigger>
            </TabsList>

            <TabsContent value="today" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Hourly Call Volume
                    </CardTitle>
                    <CardDescription>
                      Call distribution throughout the day
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={callVolumeChartConfig}
                      className="h-[300px] w-full"
                    >
                      <BarChart data={todayCallData} accessibilityLayer>
                        <CartesianGrid
                          vertical={false}
                          strokeDasharray="3 3"
                          stroke="var(--muted)"
                        />
                        <XAxis
                          dataKey="name"
                          tickLine={false}
                          axisLine={false}
                          tickMargin={8}
                        />
                        <YAxis
                          tickLine={false}
                          axisLine={false}
                          tickMargin={8}
                        />
                        <ChartTooltip
                          cursor={false}
                          content={<ChartTooltipContent indicator="dot" />}
                        />
                        <Bar
                          dataKey="calls"
                          fill="var(--color-calls)"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ChartContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Success Rate by Hour
                    </CardTitle>
                    <CardDescription>
                      Successful vs unsuccessful calls per hour
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={callVolumeChartConfig}
                      className="h-[300px] w-full"
                    >
                      <BarChart data={todayCallData} accessibilityLayer>
                        <CartesianGrid
                          vertical={false}
                          strokeDasharray="3 3"
                          stroke="var(--muted)"
                        />
                        <XAxis
                          dataKey="name"
                          tickLine={false}
                          axisLine={false}
                          tickMargin={8}
                        />
                        <YAxis
                          tickLine={false}
                          axisLine={false}
                          tickMargin={8}
                        />
                        <ChartTooltip
                          cursor={false}
                          content={<ChartTooltipContent />}
                        />
                        <Legend />
                        <Bar
                          dataKey="successful"
                          fill="var(--color-successful)"
                          radius={[2, 2, 0, 0]}
                          stackId="a"
                        />
                        <Bar
                          dataKey="unsuccessful"
                          fill="var(--color-unsuccessful)"
                          radius={[2, 2, 0, 0]}
                          stackId="a"
                        />
                      </BarChart>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="week" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Weekly Call Trends</CardTitle>
                  <CardDescription>
                    Call volume and success rate over the past 7 days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={callVolumeChartConfig}
                    className="h-[400px] w-full"
                  >
                    <BarChart data={weeklyCallData} accessibilityLayer>
                      <CartesianGrid
                        vertical={false}
                        strokeDasharray="3 3"
                        stroke="var(--muted)"
                      />
                      <XAxis
                        dataKey="name"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                      />
                      <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent />}
                      />
                      <Legend />
                      <Bar
                        dataKey="successful"
                        fill="var(--color-successful)"
                        radius={[2, 2, 0, 0]}
                        stackId="a"
                      />
                      <Bar
                        dataKey="unsuccessful"
                        fill="var(--color-unsuccessful)"
                        radius={[2, 2, 0, 0]}
                        stackId="a"
                      />
                    </BarChart>
                  </ChartContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="month" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Monthly Call Trends</CardTitle>
                  <CardDescription>
                    Call volume and patterns over the last 30 days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={callVolumeChartConfig}
                    className="h-[400px] w-full"
                  >
                    <AreaChart
                      accessibilityLayer
                      data={monthlyCallData}
                      margin={{
                        left: 12,
                        right: 12,
                        top: 5,
                        bottom: 0,
                      }}
                    >
                      <CartesianGrid
                        vertical={false}
                        strokeDasharray="3 3"
                        stroke="var(--muted)"
                      />
                      <XAxis
                        dataKey="name"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                      />
                      <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                      <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="line" />}
                      />
                      <defs>
                        <linearGradient
                          id="fillCalls"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="var(--color-calls)"
                            stopOpacity={0.8}
                          />
                          <stop
                            offset="95%"
                            stopColor="var(--color-calls)"
                            stopOpacity={0.1}
                          />
                        </linearGradient>
                        <linearGradient
                          id="fillSuccessful"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="var(--color-successful)"
                            stopOpacity={0.8}
                          />
                          <stop
                            offset="95%"
                            stopColor="var(--color-successful)"
                            stopOpacity={0.1}
                          />
                        </linearGradient>
                      </defs>
                      <Area
                        dataKey="calls"
                        type="natural"
                        fill="url(#fillCalls)"
                        fillOpacity={0.4}
                        stroke="var(--color-calls)"
                        strokeWidth={2}
                        stackId="a"
                      />
                      <Area
                        dataKey="successful"
                        type="natural"
                        fill="url(#fillSuccessful)"
                        fillOpacity={0.3}
                        stroke="var(--color-successful)"
                        strokeWidth={2}
                        stackId="b"
                      />
                    </AreaChart>
                  </ChartContainer>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
