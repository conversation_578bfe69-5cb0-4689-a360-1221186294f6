'use client';

import { useEffect, useState } from 'react';
import {
  CalendarIcon,
  Clock,
  Download,
  Phone,
  PhoneCall,
  PhoneMissed,
  RefreshCw,
} from 'lucide-react';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  AreaChart,
  Area,
} from 'recharts';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { useAuth } from '@/contexts/AuthContext';
import { fetchAdminAnalyticsData } from '@/actions/admin';
import { ApiData } from '@/lib/types';

// Sample data for charts
const dailyCallData = [
  { name: 'Mon', calls: 12, answered: 10, missed: 2 },
  { name: 'Tue', calls: 19, answered: 15, missed: 4 },
  { name: 'Wed', calls: 15, answered: 12, missed: 3 },
  { name: 'Thu', calls: 22, answered: 18, missed: 4 },
  { name: 'Fri', calls: 18, answered: 14, missed: 4 },
  { name: 'Sat', calls: 8, answered: 7, missed: 1 },
  { name: 'Sun', calls: 5, answered: 4, missed: 1 },
];

const monthlyCallData = [
  { name: 'Jan', calls: 240, answered: 200, missed: 40 },
  { name: 'Feb', calls: 300, answered: 250, missed: 50 },
  { name: 'Mar', calls: 280, answered: 230, missed: 50 },
  { name: 'Apr', calls: 320, answered: 270, missed: 50 },
  { name: 'May', calls: 350, answered: 300, missed: 50 },
  { name: 'Jun', calls: 380, answered: 330, missed: 50 },
  { name: 'Jul', calls: 400, answered: 350, missed: 50 },
  { name: 'Aug', calls: 420, answered: 370, missed: 50 },
  { name: 'Sep', calls: 390, answered: 340, missed: 50 },
  { name: 'Oct', calls: 410, answered: 360, missed: 50 },
  { name: 'Nov', calls: 430, answered: 380, missed: 50 },
  { name: 'Dec', calls: 450, answered: 400, missed: 50 },
];

const hourlyCallData = [
  { name: '8 AM', calls: 5 },
  { name: '9 AM', calls: 12 },
  { name: '10 AM', calls: 18 },
  { name: '11 AM', calls: 23 },
  { name: '12 PM', calls: 17 },
  { name: '1 PM', calls: 15 },
  { name: '2 PM', calls: 20 },
  { name: '3 PM', calls: 25 },
  { name: '4 PM', calls: 22 },
  { name: '5 PM', calls: 18 },
  { name: '6 PM', calls: 10 },
  { name: '7 PM', calls: 5 },
];

const callDurationData = [
  { name: '< 1 min', value: 30 },
  { name: '1-3 min', value: 45 },
  { name: '3-5 min', value: 15 },
  { name: '5-10 min', value: 8 },
  { name: '> 10 min', value: 2 },
];

const recentCalls = [
  {
    id: 1,
    name: 'Sarah Johnson',
    number: '(555) 123-4567',
    time: '10:30 AM',
    duration: '4m 12s',
    status: 'answered',
  },
  {
    id: 2,
    name: 'Michael Brown',
    number: '(555) 987-6543',
    time: '11:15 AM',
    duration: '2m 45s',
    status: 'answered',
  },
  {
    id: 3,
    name: 'Emily Davis',
    number: '(555) 456-7890',
    time: '12:05 PM',
    duration: '0m 0s',
    status: 'missed',
  },
  {
    id: 4,
    name: 'Robert Wilson',
    number: '(555) 789-0123',
    time: '1:30 PM',
    duration: '5m 20s',
    status: 'answered',
  },
  {
    id: 5,
    name: 'Jennifer Lee',
    number: '(555) 234-5678',
    time: '2:45 PM',
    duration: '3m 15s',
    status: 'answered',
  },
  {
    id: 6,
    name: 'David Miller',
    number: '(555) 345-6789',
    time: '3:20 PM',
    duration: '0m 0s',
    status: 'missed',
  },
  {
    id: 7,
    name: 'Jessica Taylor',
    number: '(555) 456-7890',
    time: '4:10 PM',
    duration: '1m 50s',
    status: 'answered',
  },
];

export default function CallAnalyticsPage() {
  const [date, setDate] = useState<Date>();
  const { selectedClinic } = useAdminClinic();
  const { getTokens } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<ApiData | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      if (!selectedClinic?._id) return;
  
      setAnalyticsLoading(true);
      try {
        const { accessToken, idToken } = await getTokens();
  
        if (!accessToken || !idToken) {
          console.error('Missing token(s)');
          return;
        }
  
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30); // Last 30 days
  
        const response = await fetchAdminAnalyticsData(
          accessToken,
          idToken,
          selectedClinic._id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0],
        );
  
        if (response.ok && response.data) {
          setAnalyticsData(response.data);
        } else {
          console.error('Failed to fetch analytics data:', response.error);
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setAnalyticsLoading(false);
      }
    };
  
    fetchAnalyticsData();
  }, [selectedClinic?._id, getTokens]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Call Analytics</h1>
        <div className="mt-2 sm:mt-0 flex flex-wrap gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, 'PPP') : 'Pick a date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Select defaultValue="week">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsLoading ? '...' : analyticsData?.summary.total_calls || 0}</div>
            {/* <p className="text-sm text-emerald-500 dark:text-emerald-400 mt-2">
              +12% from last month
            </p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Answered Calls
            </CardTitle>
            <PhoneCall className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,086</div>
            <p className="text-sm text-emerald-500 dark:text-emerald-400 mt-2">
              87% answer rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Missed Calls</CardTitle>
            <PhoneMissed className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">162</div>
            <p className="text-sm text-rose-500 dark:text-rose-400 mt-2">
              13% missed rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Call Duration
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsLoading? '...' : analyticsData?.summary.average_duration_in_minutes ?? 0}</div>
            {/* <p className="text-sm text-rose-500 dark:text-rose-400 mt-2">
              -8% from last month
            </p> */}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Daily</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
        </TabsList>
        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Call Volume</CardTitle>
              <CardDescription>
                Number of calls per day this week
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={dailyCallData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar
                    dataKey="answered"
                    name="Answered Calls"
                    fill="#10b981"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="missed"
                    name="Missed Calls"
                    fill="#ef4444"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Hourly Call Distribution</CardTitle>
                <CardDescription>Call volume by hour of day</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={hourlyCallData}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="var(--muted)"
                    />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        borderRadius: 'var(--radius)',
                        backgroundColor: 'var(--card)',
                        color: 'var(--card-foreground)',
                        border: '1px solid var(--muted)',
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="calls"
                      className="fill-blue-500/10 stroke-blue-500"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Call Duration</CardTitle>
                <CardDescription>Distribution of call lengths</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={callDurationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      stroke="var(--muted)"
                      dataKey="value"
                    >
                      {callDurationData.map(
                        (
                          entry: { name: string; value: number },
                          index: number,
                        ) => (
                          <Cell
                            key={`cell-${index}`}
                            className={
                              [
                                'fill-emerald-400',
                                'fill-emerald-500',
                                'fill-amber-400',
                                'fill-orange-500',
                                'fill-rose-500',
                              ][index % 5]
                            }
                          />
                        ),
                      )}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="weekly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Call Trends</CardTitle>
              <CardDescription>
                Call volume trends over the past weeks
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={dailyCallData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      borderRadius: 'var(--radius)',
                      backgroundColor: 'var(--card)',
                      color: 'var(--card-foreground)',
                      border: '1px solid var(--muted)',
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="calls"
                    name="Total Calls"
                    stroke="#6366f1"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="answered"
                    name="Answered"
                    stroke="#10b981"
                  />
                  <Line
                    type="monotone"
                    dataKey="missed"
                    name="Missed"
                    stroke="#ef4444"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Call Volume</CardTitle>
              <CardDescription>
                Call volume by month for the current year
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={monthlyCallData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      borderRadius: 'var(--radius)',
                      backgroundColor: 'var(--card)',
                      color: 'var(--card-foreground)',
                      border: '1px solid var(--muted)',
                    }}
                  />
                  <Legend />
                  <Bar
                    dataKey="answered"
                    name="Answered Calls"
                    fill="#10b981"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="missed"
                    name="Missed Calls"
                    fill="#ef4444"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Recent Calls</CardTitle>
          <CardDescription>Your most recent incoming calls</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Caller</TableHead>
                <TableHead>Phone Number</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentCalls.map((call) => (
                <TableRow key={call.id}>
                  <TableCell className="font-medium">{call.name}</TableCell>
                  <TableCell>{call.number}</TableCell>
                  <TableCell>{call.time}</TableCell>
                  <TableCell>{call.duration}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {call.status === 'answered' ? (
                        <>
                          <div className="h-2.5 w-2.5 rounded-full bg-emerald-500 mr-2"></div>
                          <span>Answered</span>
                        </>
                      ) : (
                        <>
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>
                          <span>Missed</span>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
