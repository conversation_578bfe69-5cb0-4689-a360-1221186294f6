'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Shield,
  ShieldAlert,
  MoreHorizontal,
  Trash,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { useAuth } from '@/contexts/AuthContext';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { toast } from 'sonner';
import { AdminUser, IUser, getAllAdmins } from '@/actions/admin';

export default function AdminManagementPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [adminList, setAdminList] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);

  const { getTokens } = useAuth();
  const { selectedClinic } = useAdminClinic();

  useEffect(() => {
    async function fetchAdmins() {
      try {
        setLoading(true);
        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          toast.error('Authentication required');
          setLoading(false);
          return;
        }
        const res = await getAllAdmins(accessToken, idToken);
        if (res.ok && Array.isArray(res.data)) {
          // Ensure dates are Date objects (if API returns strings)
          const admins = res.data.map((admin) => ({
            ...admin,
            created_at: new Date(admin.created_at),
            updated_at: new Date(admin.updated_at),
            last_login: admin.last_login
              ? new Date(admin.last_login)
              : undefined,
          }));
          setAdminList(admins);
        } else {
          toast.error(res.error || 'Error fetching admin list');
        }
      } catch (error) {
        console.error('Failed to fetch admins:', error);
        toast.error('Error fetching admin list');
      } finally {
        setLoading(false);
      }
    }
    fetchAdmins();
  }, [getTokens]);

  const filteredAdmins = adminList.filter((admin) => {
    const name =
      `${admin.first_name || ''} ${admin.last_name || ''}`.toLowerCase();
    const query = searchQuery.toLowerCase();
    return (
      name.includes(query) ||
      admin.email.toLowerCase().includes(query) ||
      (admin.admin_level?.toLowerCase() ?? '').includes(query)
    );
  });

  const getDisplayName = (admin: IUser) => {
    return (
      `${admin.first_name || ''} ${admin.last_name || ''}`.trim() || admin.email
    );
  };

  const getInitials = (admin: IUser) => {
    const first = admin.first_name || '';
    const last = admin.last_name || '';
    const initials = `${first.charAt(0)}${last.charAt(0)}`.toUpperCase();
    return initials || admin.email.charAt(0).toUpperCase();
  };

  const formatJoinDate = (date: Date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Admin Management
          </h1>
          {selectedClinic && (
            <p className="text-sm text-muted-foreground mt-1">
              Managing global admins
            </p>
          )}
        </div>
        {/* Uncomment and wire buttons as needed
        <div className="mt-2 sm:mt-0 flex gap-2">
          <Button variant="outline" onClick={fetchAdmins} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Add Admin
          </Button>
        </div>
        */}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Admins</CardTitle>
          <CardDescription>View and manage admin-level access</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex w-full max-w-sm items-center space-x-2">
              <Input
                placeholder="Search admins..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-9"
              />
              <Button variant="outline" size="sm" className="h-9 px-4 shrink-0">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Admin Level</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        Loading admins...
                      </TableCell>
                    </TableRow>
                  ) : filteredAdmins.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No admins found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAdmins.map((admin) => (
                      <TableRow key={admin.uid}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={`https://xvatar.vercel.app/api/avatar/${admin.email}.svg?rounded=120&size=240`}
                              />
                              <AvatarFallback>
                                {getInitials(admin)}
                              </AvatarFallback>
                            </Avatar>
                            <span>{getDisplayName(admin)}</span>
                          </div>
                        </TableCell>
                        <TableCell>{admin.email}</TableCell>
                        <TableCell className="capitalize flex items-center gap-2">
                          {admin.admin_level === 'owner' ? (
                            <ShieldAlert className="size-5 text-rose-500" />
                          ) : (
                            <Shield className="size-5 text-blue-500" />
                          )}
                          {admin.admin_level || 'viewer'}
                        </TableCell>
                        <TableCell>
                          {formatJoinDate(admin.created_at)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem className="text-red-600">
                                <Trash className="mr-2 h-4 w-4" />
                                <span>Remove Admin</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
