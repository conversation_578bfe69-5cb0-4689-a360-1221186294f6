'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Link from 'next/link';
import robot from '@/assets/637d05fde12331fe02047e483cc84f91bfd5d0a8.png';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import { confirmSignIn, signIn, fetchAuthSession } from 'aws-amplify/auth';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { getUserClinics } from '@/services/clinicService';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const auth = useAuth();

  const flowType = searchParams.get('flow-type');
  const isAdminFlow = flowType === 'admin';

  // Check if user is coming from an invitation
  const hasPendingInvite =
    typeof window !== 'undefined' &&
    (localStorage.getItem('pendingInviteToken') ||
      localStorage.getItem('redirectAfterAuth'));

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    try {
      const { nextStep } = await signIn({
        username: values.email,
        options: {
          authFlowType: 'USER_AUTH',
          preferredChallenge: 'EMAIL_OTP',
        },
      });

      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description: 'Verification code sent to your email',
        });
      } else {
        toast.error('Unhandled Step', {
          description: nextStep.signInStep,
        });
      }
    } catch (error) {
      const errorName = (error as Error).name;
      if (
        errorName === 'UserNotFoundException' ||
        errorName === 'NotAuthorizedException'
      ) {
        toast.error((error as Error).message, {
          description: 'New user? Sign up first.',
        });
        setTimeout(() => {
          router.push('/signup');
        }, 3000);
      } else {
        toast.error('Login Failed', {
          description: 'An unexpected error occurred. Please try again.',
        });
      }
    } finally {
      setIsLoading(false);
    }
  }

  const onOtpSubmit = async (otp: string) => {
    try {
      const response = await confirmSignIn({
        challengeResponse: otp,
      });
      if (response.isSignedIn) {
        await onOtpVerified();
      } else {
        toast.error('Sign-in Incomplete', {
          description:
            response.nextStep?.signInStep ||
            'Could not complete sign-in with OTP. Please try again.',
        });
      }
    } catch (error) {
      console.error('Error confirming sign-in:', error);
      toast.error('Verification Failed', {
        description: 'Invalid verification code. Please try again.',
      });
    }
  };

  async function onOtpVerified() {
    try {
      console.log(
        'onOtpVerified - Starting verification process post-Amplify confirm',
      );

      console.log(
        'onOtpVerified - Calling auth.checkAuth() to refresh context',
      );
      const userDetails = await auth.checkAuth();

      if (!userDetails) {
        toast.error('Authentication Failed', {
          description:
            'Your session could not be verified. Please try logging in again.',
        });
        setShowOtpVerification(false);
        form.reset();
        return;
      }

      const currentIsAdmin = userDetails.isAdmin;

      console.log('onOtpVerified - Admin check from AuthContext:', {
        isAdmin: currentIsAdmin,
        isAdminFlow,
      });

      if (isAdminFlow && !currentIsAdmin) {
        toast.error('Access Denied', {
          description: 'You do not have admin privileges to access this area.',
        });
        await auth.logout();
        router.push('/login');
        return;
      }

      // For admin users, redirect to admin dashboard
      if (isAdminFlow && currentIsAdmin) {
        router.push('/admin/dashboard');
        return;
      } else if (currentIsAdmin) {
        router.push('/admin/dashboard');
        return;
      }

      // Check if there's a pending invitation or redirect
      const redirectAfterAuth = localStorage.getItem('redirectAfterAuth');
      const pendingInviteToken = localStorage.getItem('pendingInviteToken');

      if (redirectAfterAuth) {
        // Clear the redirect flag and go to the stored URL
        localStorage.removeItem('redirectAfterAuth');
        router.push(redirectAfterAuth);
        return;
      }

      if (pendingInviteToken) {
        // Clear the token and redirect to accept-invite
        localStorage.removeItem('pendingInviteToken');
        router.push(`/accept-invite?token=${pendingInviteToken}`);
        return;
      }

      // For regular users, check if they have clinics
      try {
        const session = await fetchAuthSession();
        const accessToken = session.tokens?.accessToken?.toString();

        if (accessToken) {
          const clinicsResult = await getUserClinics(accessToken);

          if (
            clinicsResult.ok &&
            clinicsResult.data &&
            clinicsResult.data.length > 0
          ) {
            // User has clinics, redirect to dashboard
            router.push('/dashboard');
          } else {
            // User has no clinics, redirect to onboarding
            router.push('/onboarding');
          }
        } else {
          // Fallback to dashboard if no token
          router.push('/dashboard');
        }
      } catch (clinicError) {
        console.error('Error checking user clinics:', clinicError);
        // Fallback to dashboard on error
        router.push('/dashboard');
      }
    } catch (error) {
      console.error(
        'onOtpVerified - Error during post-OTP verification steps:',
        error,
      );
      toast.error('Verification Process Error', {
        description:
          'An error occurred after OTP validation. Please try again.',
      });
      setShowOtpVerification(false);
      form.reset();
    }
  }

  return (
    <div className="min-h-dvh items-center grid md:grid-cols-2 grid-cols-1">
      <div className="absolute top-10 left-20 text-xl font-bold">
        <span className="text-primary">Smart</span>
        Reception
      </div>
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow="SIGNIN"
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <div className="w-full mx-auto">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold">
                {isAdminFlow ? 'Admin Login' : 'Login'}
              </CardTitle>
              <CardDescription>
                {isAdminFlow
                  ? 'Enter your admin email to access the admin dashboard'
                  : hasPendingInvite
                    ? 'Log in to accept your clinic invitation'
                    : 'Enter your email to receive a verification code'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? 'Sending OTP...' : 'Send OTP'}
                  </Button>
                </form>
              </Form>
              <div className="mt-4 text-center text-sm space-y-2">
                <div>
                  New user?{' '}
                  <Link href="/signup" className="text-primary hover:underline">
                    Sign up here
                  </Link>
                </div>
                {isAdminFlow && (
                  <div>
                    Regular user?{' '}
                    <Link
                      href="/login"
                      className="text-primary hover:underline"
                    >
                      User login
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      <div className="h-dvh w-full hidden md:block overflow-hidden">
        <Image
          src={robot}
          alt="Logo"
          width={900}
          height={900}
          className="mx-auto object-center object-cover size-full"
        />
      </div>
    </div>
  );
}
