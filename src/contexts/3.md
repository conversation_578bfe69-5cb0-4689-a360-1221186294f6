Doctor dashboard

Call anlyics

1. first 4 metrics -> lifetime, send end date last 1 year
2. 3 tabs -> Today, 7 days, last 30 days
3. Below tab, Total calls:, Avg Duration (2 texts)
4. <ClinicProfile /> -> Anish

- 3 tabs -> raj
- 1 -> Profile (multiple sections)
  - - Specialities comp as per Figma -> Anish
- 2 -> Agent Details -> Raj
- 3 -> Knowledge Bases (view, edit, remove, upload) -> Raj

9. Invite auth flow -> State persistence & Redirection -> Use query params
10. Clinic Dashborad -> 1st 4 metrics lifetime, pass 1 year end date -> Anish to make component

- 3 Sections
  - Today
  - Last 7 days
  - last 30 days
  - show total calls & avg duration in each section
    <ClinicDahboardComp /> -> To be consumed by Raj

---

Admin

1. Clinic Dahsborad -> consume comp
2. manage users is breaking
3. Call analytics -> Consume compo by An<PERSON>
4. <PERSON>rrow invitation
5. Clinic profile with 3 tab
6. Delete clinic integration
7. View clinic details / edit -> take to clinic profile
